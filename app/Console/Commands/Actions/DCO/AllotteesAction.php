<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class AllotteesAction extends Action
{
    protected $signature = 'datasource:allottees {flowId} {parentId} {input}';
    protected $description = 'Allottees List';

    protected $formatter = [
        "id" => "",
        "member_id" => "",
        "fk_unit_id" => "",
        "salute" => "",
        "member_name" => "",
        "member_email_id" => "",
        "member_mobile_number" => "",
        "effective_date" => "",
        "member_type_name" => "",
        "member_intercom" => "",
        "member_status" => "",
        // "status" => "",
        "soc_building_name" => "",
        "unit_flat_number" => "",
        "building_unit" => "",
        "approved" => "",
        "user_id" => ""
    ];

    protected $formatterByKeys = ['id'];
    protected $mapper = ['id' => "member_master.fk_unit_id"];

    public function apply()
    {
        try  {
            $page = $this->input['page'] ?? 1;
            $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 1000);
            $offset = ($page - 1) * $per_page;
            $filters = $this->input['filters'] ?? [];
            $memberName = $filters['memberName'] ?? '';
            $memberNumber = $filters['memberNumber'] ?? '';
            $unitNumber = $filters['unitNumber'] ?? '';
            $building = $filters['building'] ?? '';
            $buildingUnitNumber = $filters['buildingUnitNumber'] ?? '';
            $memberTypeName = $filters['memberTypeName'] ?? '';
    
            $currentTab = $this->input['current_tab'] ?? 'approved';
            $unitId = $this->input['unit_id'] ?? $this->input['id'] ?? null;
            $currentDate = $this->getCurrentDate('database');
            $cancelDate = "0000-00-00";
    
            $approved = $this->determineApprovalStatus($currentTab);
    
            $columns = $this->getMemberColumns($currentTab);
    
            $query = $this->buildBaseQuery($columns, $currentDate, $cancelDate, $approved, $currentTab);
            
            $query = $this->applyFilters($query, $unitId, $memberName, $memberNumber, $unitNumber, $building, $buildingUnitNumber, $memberTypeName);
            

            // Apply pagination
            $count = $query->get()->count();


            // $results = $query->offset($offset)->limit($per_page);
            // Fetch the results
            // $results = $query->get()->toArray();
    
            $results = $query->get()->toArray();
            // Extract all member IDs in bulk
            $allMemberIds = array_unique(array_merge(...array_map(fn($result) => explode(',', $result->member_id), $results)));
    
            // Fetch member details in a single query
            $memberDetails = $this->fetchAllMemberDetails($allMemberIds);
    
            // Map member details back to results
            $memberDetailsGrouped = $this->groupBy($memberDetails, 'member_id');
            foreach ($results as &$result) {
                $result->disable = 1;
                $memberIds = explode(',', $result->member_id);
                $result->rows = array_values(array_intersect_key($memberDetailsGrouped, array_flip($memberIds)));
            }
    
            $result_approved   = [];
            $result_unapproved = [];
            $result_pending    = [];

            foreach ($results as $familyMember) {
                // 1) Walk its rows and bucket them by status and mcr_id
                $approvedRows   = [];
                $unapprovedRows = [];
                $pendingRows    = [];

                foreach ($familyMember->rows as $row) {
                    // Check if mcr_id is not null for pending requests
                    if (isset($row->mcr_id) && $row->mcr_id !== null && $row->mcr_id !== '') {
                        $pendingRows[] = $row;
                    } else if ($row->status === 1) {
                        $approvedRows[] = $row;
                    } else {
                        $unapprovedRows[] = $row;
                    }
                }

                // 2) If any status==1 rows exist, clone the parent and attach just those
                if (!empty($approvedRows)) {
                    $clone = clone $familyMember;
                    $clone->rows = $approvedRows;
                    $result_approved[] = $clone;
                }

                // 3) If any status==0 rows exist, clone the parent and attach just those
                if (!empty($unapprovedRows)) {
                    $clone = clone $familyMember;
                    $clone->rows = $unapprovedRows;
                    $result_unapproved[] = $clone;
                }

                // 4) If any mcr_id rows exist, clone the parent and attach just those
                if (!empty($pendingRows)) {
                    $clone = clone $familyMember;
                    $clone->rows = $pendingRows;
                    $result_pending[] = $clone;
                }
            }

            if ($currentTab == 'primary member change requests') {
                foreach ($results as &$result) {
                    $result->disable = 1;
                    unset($result->user_id); 
                   
                    $memberIds = explode(',', $result->member_id);
                    $result->rows = array_values(array_intersect_key($memberDetailsGrouped, array_flip($memberIds)));

                    // Add new member details from parent to child rows if mcr_id exists
                    if (isset($result->mcr_id) && $result->mcr_id !== null && $result->mcr_id !== '') {
                        foreach ($result->rows as &$row) {
                            // Add new member details to each row
                            $row->new_member_name = $result->new_member_first_name . ' ' . $result->new_member_last_name ?? null;
                            $row->new_member_last_name = $result->new_member_last_name ?? null;
                            $row->new_member_email_id = $result->new_member_email_id ?? null;
                            $row->new_member_mobile_number = $result->new_member_mobile_number ?? null;
                            $row->new_user_id = $result->new_user_id ?? null;
                            $row->changerequeststatus = $result->changerequeststatus ?? null;
                        }
                    }

                    // Remove/nullify the specific keys from parent as requested (for all results)
                    unset($result->new_member_first_name);
                    unset($result->new_member_last_name);
                    unset($result->new_member_email_id);
                    unset($result->new_member_mobile_number);
                    unset($result->new_user_id);
                }
            }
            
            if ($currentTab == 'unapproved') {
                $this->data = $result_unapproved;
            } else if ($currentTab == 'approved') {
                $this->data = $result_approved;
            } else if ($currentTab == 'pending' || $currentTab == 'primary member change requests') {
                $this->data = $result_pending;
            } else {
                $this->data = [];
            }

            // Post-process to remove new member details from parent objects for change requests tab
            if ($currentTab == 'primary member change requests') {
                foreach ($this->data as &$item) {
                    unset($item->new_member_first_name);
                    unset($item->new_member_last_name);
                    unset($item->new_member_email_id);
                    unset($item->new_member_mobile_number);
                    unset($item->new_user_id);
                }
            }

            $this->meta['pagination']['total'] = $count;
        } catch (\Exception $e) {
            dd($e);
        }
    }


    private function determineApprovalStatus($currentTab)
    {

        switch (strtolower($currentTab)) {
            case 'unapproved':
                return '0';
            case 'primary member change requests':
                return '2';
            default:
                return '1';
        }
    }

    private function getMemberColumns($currentTab)
    {
        $columns = 'member_master.fk_unit_id AS id,
                    GROUP_CONCAT(member_master.id) AS member_id,
                    GROUP_CONCAT(member_master.approved) AS approved,
                    member_master.fk_unit_id AS fk_unit_id,
                    CONCAT(units.soc_building_name, "-", units.unit_flat_number) AS building_unit,
                    member_master.fk_unit_id,
                    member_master.user_id AS user_id,
                    units.soc_building_name AS soc_building_name,
                    units.unit_flat_number AS unit_flat_number';

        if (strtolower($currentTab) === 'primary member change requests') {
            $columns .= ',
                GROUP_CONCAT(member_change_request.id) AS mcr_id,
                GROUP_CONCAT(member_change_request.member_first_name) AS new_member_first_name,
                GROUP_CONCAT(member_change_request.member_last_name) AS new_member_last_name,
                GROUP_CONCAT(member_change_request.member_email_id) AS new_member_email_id,
                GROUP_CONCAT(member_change_request.member_mobile_number) AS new_member_mobile_number,
                GROUP_CONCAT(IF(member_change_request.user_id IS NOT NULL AND member_change_request.user_id != 0, member_change_request.user_id, NULL)) AS new_user_id,
                GROUP_CONCAT(member_change_request.changerequeststatus) AS changerequeststatus';
        }

        return $columns;
    }

    private function buildBaseQuery($columns, $currentDate, $cancelDate, $approved, $currentTab)
    {
        $query = $this->tenantDB()->table('chsone_members_master AS member_master')
        ->selectRaw($columns)
        ->join('chsone_units_master AS units', 'units.unit_id', '=', 'member_master.fk_unit_id')
        ->join('chsone_member_type_master AS member_type', 'member_master.member_type_id', '=', 'member_type.member_type_id');

        // Add join to member_change_request if needed
        if (strtolower($currentTab) === 'primary member change requests') {
            $query->leftJoin('chsone_member_change_requests AS member_change_request', 'member_change_request.member_id', '=', 'member_master.id')
            ->where('member_change_request.changerequeststatus', null);
        }

        return $query
        ->groupBy('member_master.fk_unit_id')
        ->orderBy('units.soc_building_name')
        ->orderBy('units.unit_flat_number');

    }

    private function applyFilters($query, $unitId, $memberName, $memberNumber, $unitNumber, $building, $buildingUnitNumber, $memberTypeName)
    {
        if ($unitId) {
            // $query->where("member_master.fk_unit_id", $unitId);

            // get route to switch query
            $currentRoute = Route::current();

            // Get the route URI pattern (e.g., "member/register/{id}")
            $routeUri = $currentRoute->uri();

            if ($routeUri == 'api/admin/vendorbill/add_billable/{id}') {
                $query->where("member_master.fk_unit_id", $unitId);
                $query->where("member_master.member_type_id", 1);
            } else {
                $query->where("member_master.fk_unit_id", $unitId);
            }
        }
        if ($memberName) {
            $query->whereRaw("CONCAT(LOWER(member_master.member_first_name), ' ', LOWER(member_master.member_last_name)) LIKE ?", ['%' . strtolower($memberName) . '%']);
        }
        if ($memberNumber) {
            $query->where("member_master.member_mobile_number", $memberNumber);
        }
        if ($unitNumber) {
            $query->whereRaw("LOWER(units.unit_flat_number) LIKE ?", ['%' . strtolower($unitNumber) . '%']);
        }
        if ($building) {
            $query->whereRaw("LOWER(units.soc_building_name) LIKE ?", ['%' . strtolower($building) . '%']);
        }
        if ($buildingUnitNumber) {
            $query->whereRaw("LOWER(units.unit_flat_number) LIKE ?", ['%' . strtolower($buildingUnitNumber) . '%']);
        }

        if ($memberTypeName) {
            $query->whereIn('member_type.member_type_name', (array)$memberTypeName);
        }

        return $query;
    }

    private function fetchAllMemberDetails(array $memberIds)
    {
        return $this->tenantDB()->table('chsone_members_master AS member_master')
            ->leftJoin('chsone_member_type_master AS member_type', 'member_type.member_type_id', '=', 'member_master.member_type_id')
            ->leftJoin('chsone_member_change_requests AS member_change_request', 'member_change_request.member_id', '=', 'member_master.id')
            ->select(
                'member_master.id as member_id',
                'member_master.user_id',
                'member_master.salute',
                'member_master.member_first_name',
                'member_master.member_last_name',
                DB::raw("CONCAT_WS(' ', member_master.salute, member_master.member_first_name, member_master.member_last_name) AS building_unit"),
                'member_master.member_dob',
                'member_master.member_gender',
                'member_master.member_email_id',
                'member_master.member_mobile_number',
                'member_master.effective_date',
                'member_type.member_type_name',
                'member_master.status',
                'member_change_request.id as mcr_id',
                // DB::raw("CASE WHEN member_master.status = 0 THEN 'Inactive' WHEN member_master.status = 1 THEN 'Active' ELSE 'unknown' END AS status") // Map status to strings
            )
            ->whereIn('member_master.id', $memberIds)
            ->get()
            ->toArray();
    }

    private function groupBy($array, $key)
    {
        $grouped = [];
        foreach ($array as $item) {
            $grouped[$item->$key] = $item;
        }
        return $grouped;
    }
}
